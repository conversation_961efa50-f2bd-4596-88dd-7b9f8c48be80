import React, { useState } from 'react';
import { Co<PERSON>, <PERSON>, <PERSON>, Moon } from 'lucide-react';

interface ButtonConfig {
  text: string;
  // Light mode colors
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  hoverBackgroundColor: string;
  hoverTextColor: string;
  hoverBorderColor: string;
  // Dark mode colors
  darkBackgroundColor: string;
  darkTextColor: string;
  darkBorderColor: string;
  darkHoverBackgroundColor: string;
  darkHoverTextColor: string;
  darkHoverBorderColor: string;
  // Other properties
  borderWidth: number;
  borderStyle: string;
  borderRadius: number;
  width: number;
  height: number;
  paddingX: number;
  paddingY: number;
  fontSize: number;
  fontWeight: string;
  boxShadow: string;
  transition: string;
  supportDarkMode: boolean;
}

const ButtonGenerator: React.FC = () => {
  const [config, setConfig] = useState<ButtonConfig>({
    text: 'Click Me',
    // Light mode colors
    backgroundColor: '#3b82f6',
    textColor: '#ffffff',
    borderColor: '#3b82f6',
    hoverBackgroundColor: '#2563eb',
    hoverTextColor: '#ffffff',
    hoverBorderColor: '#2563eb',
    // Dark mode colors
    darkBackgroundColor: '#1e40af',
    darkTextColor: '#f1f5f9',
    darkBorderColor: '#1e40af',
    darkHoverBackgroundColor: '#1d4ed8',
    darkHoverTextColor: '#f1f5f9',
    darkHoverBorderColor: '#1d4ed8',
    // Other properties
    borderWidth: 2,
    borderStyle: 'solid',
    borderRadius: 8,
    width: 140,
    height: 44,
    paddingX: 24,
    paddingY: 12,
    fontSize: 16,
    fontWeight: '500',
    boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)',
    transition: 'all 0.2s ease-in-out',
    supportDarkMode: true,
  });

  const [exportFormat, setExportFormat] = useState('css');
  const [tailwindFormat, setTailwindFormat] = useState<'className' | 'class'>('className');
  const [copied, setCopied] = useState(false);
  const [previewMode, setPreviewMode] = useState<'light' | 'dark'>('light');

  const updateConfig = (key: keyof ButtonConfig, value: string | number | boolean) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const getButtonStyle = (isHover: boolean = false, isDark: boolean = false): React.CSSProperties => {
    const colors = isDark ? {
      backgroundColor: isHover ? config.darkHoverBackgroundColor : config.darkBackgroundColor,
      color: isHover ? config.darkHoverTextColor : config.darkTextColor,
      borderColor: isHover ? config.darkHoverBorderColor : config.darkBorderColor,
    } : {
      backgroundColor: isHover ? config.hoverBackgroundColor : config.backgroundColor,
      color: isHover ? config.hoverTextColor : config.textColor,
      borderColor: isHover ? config.hoverBorderColor : config.borderColor,
    };

    return {
      ...colors,
      borderWidth: `${config.borderWidth}px`,
      borderStyle: config.borderStyle,
      borderRadius: `${config.borderRadius}px`,
      width: `${config.width}px`,
      height: `${config.height}px`,
      paddingLeft: `${config.paddingX}px`,
      paddingRight: `${config.paddingX}px`,
      paddingTop: `${config.paddingY}px`,
      paddingBottom: `${config.paddingY}px`,
      fontSize: `${config.fontSize}px`,
      fontWeight: config.fontWeight,
      boxShadow: config.boxShadow,
      transition: config.transition,
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'inherit',
      outline: 'none',
    };
  };

  const generateCSS = (): string => {
    const lightStyle = getButtonStyle(false, false);
    const lightHoverStyle = getButtonStyle(true, false);
    const darkStyle = getButtonStyle(false, true);
    const darkHoverStyle = getButtonStyle(true, true);
    
    let css = `.custom-button {
  background-color: ${lightStyle.backgroundColor};
  color: ${lightStyle.color};
  border: ${lightStyle.borderWidth} ${lightStyle.borderStyle} ${lightStyle.borderColor};
  border-radius: ${lightStyle.borderRadius};
  width: ${lightStyle.width};
  height: ${lightStyle.height};
  padding: ${lightStyle.paddingTop} ${lightStyle.paddingRight} ${lightStyle.paddingBottom} ${lightStyle.paddingLeft};
  font-size: ${lightStyle.fontSize};
  font-weight: ${lightStyle.fontWeight};
  box-shadow: ${lightStyle.boxShadow};
  transition: ${lightStyle.transition};
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
  outline: none;
}

.custom-button:hover {
  background-color: ${lightHoverStyle.backgroundColor};
  color: ${lightHoverStyle.color};
  border-color: ${lightHoverStyle.borderColor};
}`;

    if (config.supportDarkMode) {
      css += `

@media (prefers-color-scheme: dark) {
  .custom-button {
    background-color: ${darkStyle.backgroundColor};
    color: ${darkStyle.color};
    border-color: ${darkStyle.borderColor};
  }
  
  .custom-button:hover {
    background-color: ${darkHoverStyle.backgroundColor};
    color: ${darkHoverStyle.color};
    border-color: ${darkHoverStyle.borderColor};
  }
}`;
    }

    return css;
  };

  const generateTailwindCSS = (): string => {
    const getColorClass = (color: string, prefix: string = '') => {
      return `${prefix}[${color}]`;
    };

    const borderStyleClass = config.borderStyle === 'solid' ? 'border-solid' : 
                           config.borderStyle === 'dashed' ? 'border-dashed' : 
                           config.borderStyle === 'dotted' ? 'border-dotted' : 'border-solid';

    const fontWeightClass = config.fontWeight === '300' ? 'font-light' :
                           config.fontWeight === '400' ? 'font-normal' :
                           config.fontWeight === '500' ? 'font-medium' :
                           config.fontWeight === '600' ? 'font-semibold' :
                           config.fontWeight === '700' ? 'font-bold' : 'font-medium';

    let classes = [
      `bg-${getColorClass(config.backgroundColor)}`,
      `text-${getColorClass(config.textColor)}`,
      `border-[${config.borderWidth}px]`,
      `border-${getColorClass(config.borderColor)}`,
      borderStyleClass,
      `rounded-[${config.borderRadius}px]`,
      `w-[${config.width}px]`,
      `h-[${config.height}px]`,
      `px-[${config.paddingX}px]`,
      `py-[${config.paddingY}px]`,
      `text-[${config.fontSize}px]`,
      fontWeightClass,
      `hover:bg-${getColorClass(config.hoverBackgroundColor)}`,
      `hover:text-${getColorClass(config.hoverTextColor)}`,
      `hover:border-${getColorClass(config.hoverBorderColor)}`,
      'inline-flex',
      'items-center',
      'justify-center',
      'cursor-pointer',
      'outline-none',
      'transition-all',
      'duration-200',
      'ease-in-out'
    ];

    if (config.supportDarkMode) {
      classes = classes.concat([
        `dark:bg-${getColorClass(config.darkBackgroundColor)}`,
        `dark:text-${getColorClass(config.darkTextColor)}`,
        `dark:border-${getColorClass(config.darkBorderColor)}`,
        `dark:hover:bg-${getColorClass(config.darkHoverBackgroundColor)}`,
        `dark:hover:text-${getColorClass(config.darkHoverTextColor)}`,
        `dark:hover:border-${getColorClass(config.darkHoverBorderColor)}`
      ]);
    }

    const classString = classes.join(' ');
    const attributeName = tailwindFormat === 'className' ? 'className' : 'class';
    
    return `<button ${attributeName}="${classString}">
  ${config.text}
</button>`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getExportContent = () => {
    switch (exportFormat) {
      case 'css':
        return generateCSS();
      case 'tailwind':
        return generateTailwindCSS();
      default:
        return generateCSS();
    }
  };

  const borderStyles = ['solid', 'dashed', 'dotted', 'double'];
  const fontWeights = [
    { value: '300', label: 'Light' },
    { value: '400', label: 'Normal' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semibold' },
    { value: '700', label: 'Bold' },
  ];

  const shadowPresets = [
    { label: 'None', value: 'none' },
    { label: 'Small', value: '0 1px 2px 0 rgb(0 0 0 / 0.05)' },
    { label: 'Medium', value: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)' },
    { label: 'Large', value: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06)' },
    { label: 'Extra Large', value: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-neutral-100 dark:from-neutral-900 dark:via-black dark:to-neutral-950">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row gap-8 py-12 px-4 md:px-8">
        {/* Sidebar Controls */}
        <aside className="w-full md:w-80 flex-shrink-0 space-y-8 sticky top-8 self-start">
          {/* Section: Text */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-6 animate-fade-in">
            <h2 className="text-xl font-bold mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Button Text</h2>
            <input
              type="text"
              value={config.text}
              onChange={(e) => updateConfig('text', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-neutral-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-medium bg-gray-50 dark:bg-neutral-800 transition"
              placeholder="Button label..."
            />
          </section>

          {/* Section: Colors */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-6 animate-fade-in">
            <h2 className="text-xl font-bold mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Colors</h2>
            {/* Light Colors */}
            <div className="mb-6">
              <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2 text-sm uppercase tracking-wider">Light Mode</h3>
              <div className="grid grid-cols-1 gap-3">
                {/* Background */}
                <ColorInput label="Background" value={config.backgroundColor} onChange={v => updateConfig('backgroundColor', v)} />
                <ColorInput label="Text" value={config.textColor} onChange={v => updateConfig('textColor', v)} />
                <ColorInput label="Border" value={config.borderColor} onChange={v => updateConfig('borderColor', v)} />
                <ColorInput label="Hover BG" value={config.hoverBackgroundColor} onChange={v => updateConfig('hoverBackgroundColor', v)} />
                <ColorInput label="Hover Text" value={config.hoverTextColor} onChange={v => updateConfig('hoverTextColor', v)} />
                <ColorInput label="Hover Border" value={config.hoverBorderColor} onChange={v => updateConfig('hoverBorderColor', v)} />
              </div>
            </div>
            {/* Dark Colors */}
            {config.supportDarkMode && (
              <div>
                <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2 text-sm uppercase tracking-wider">Dark Mode</h3>
                <div className="grid grid-cols-1 gap-3">
                  <ColorInput label="Background" value={config.darkBackgroundColor} onChange={v => updateConfig('darkBackgroundColor', v)} />
                  <ColorInput label="Text" value={config.darkTextColor} onChange={v => updateConfig('darkTextColor', v)} />
                  <ColorInput label="Border" value={config.darkBorderColor} onChange={v => updateConfig('darkBorderColor', v)} />
                  <ColorInput label="Hover BG" value={config.darkHoverBackgroundColor} onChange={v => updateConfig('darkHoverBackgroundColor', v)} />
                  <ColorInput label="Hover Text" value={config.darkHoverTextColor} onChange={v => updateConfig('darkHoverTextColor', v)} />
                  <ColorInput label="Hover Border" value={config.darkHoverBorderColor} onChange={v => updateConfig('darkHoverBorderColor', v)} />
                </div>
              </div>
            )}
          </section>

          {/* Section: Border & Radius */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-6 animate-fade-in">
            <h2 className="text-xl font-bold mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Border & Radius</h2>
            <div className="flex items-center gap-3 mb-4">
              <span className="text-sm text-gray-600 dark:text-gray-300 w-20">Color</span>
              <input type="color" value={config.borderColor} onChange={e => updateConfig('borderColor', e.target.value)} className="w-8 h-8 rounded-full border border-gray-300 dark:border-neutral-700" />
              <input type="number" value={config.borderWidth} onChange={e => updateConfig('borderWidth', parseInt(e.target.value))} min="0" max="10" className="w-16 px-2 py-1 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100" />
              <select value={config.borderStyle} onChange={e => updateConfig('borderStyle', e.target.value)} className="px-2 py-1 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100">
                {borderStyles.map(style => (<option key={style} value={style}>{style}</option>))}
              </select>
            </div>
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Radius: {config.borderRadius}px</label>
              <input type="range" min="0" max="50" value={config.borderRadius} onChange={e => updateConfig('borderRadius', parseInt(e.target.value))} className="w-full accent-blue-500" />
            </div>
          </section>

          {/* Section: Shadow */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-6 animate-fade-in">
            <h2 className="text-xl font-bold mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Shadow</h2>
            <select value={config.boxShadow} onChange={e => updateConfig('boxShadow', e.target.value)} className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100">
              {shadowPresets.map(shadow => (<option key={shadow.label} value={shadow.value}>{shadow.label}</option>))}
            </select>
          </section>

          {/* Section: Size & Typography */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-6 animate-fade-in">
            <h2 className="text-xl font-bold mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Size & Typography</h2>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Width: {config.width}px</label>
              <input type="range" min="60" max="400" value={config.width} onChange={e => updateConfig('width', parseInt(e.target.value))} className="w-full accent-blue-500" />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Height: {config.height}px</label>
              <input type="range" min="24" max="80" value={config.height} onChange={e => updateConfig('height', parseInt(e.target.value))} className="w-full accent-blue-500" />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Font Size: {config.fontSize}px</label>
              <input type="range" min="10" max="32" value={config.fontSize} onChange={e => updateConfig('fontSize', parseInt(e.target.value))} className="w-full accent-blue-500" />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Font Weight</label>
              <select value={config.fontWeight} onChange={e => updateConfig('fontWeight', e.target.value)} className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100">
                {fontWeights.map(weight => (<option key={weight.value} value={weight.value}>{weight.label}</option>))}
              </select>
            </div>
          </section>

          {/* Section: Dark Mode Toggle */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-6 animate-fade-in flex items-center justify-between">
            <span className="text-base font-semibold text-gray-700 dark:text-gray-300">Dark Mode Support</span>
            <button
              onClick={() => updateConfig('supportDarkMode', !config.supportDarkMode)}
              className={`relative inline-flex h-7 w-14 items-center rounded-full transition-colors duration-200 ${config.supportDarkMode ? 'bg-blue-600' : 'bg-gray-300'}`}
            >
              <span className={`inline-block h-5 w-5 transform rounded-full bg-white shadow transition-transform duration-200 ${config.supportDarkMode ? 'translate-x-7' : 'translate-x-1'}`} />
            </button>
          </section>
        </aside>

        {/* Main Content: Preview & Export */}
        <main className="flex-1 flex flex-col gap-8">
          {/* Preview */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-xl border border-gray-200 dark:border-neutral-800 p-8 flex flex-col items-center animate-fade-in">
            <div className="flex items-center justify-between w-full mb-6">
              <h2 className="text-2xl font-bold text-blue-700 dark:text-blue-300 flex items-center gap-2"><Eye className="w-6 h-6" /> Preview</h2>
              {config.supportDarkMode && (
                <div className="flex space-x-2 bg-gray-100 dark:bg-neutral-800 rounded-lg p-1">
                  <button
                    onClick={() => setPreviewMode('light')}
                    className={`px-3 py-1 rounded text-base font-medium transition-colors duration-200 ${previewMode === 'light' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                  >
                    <Sun className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setPreviewMode('dark')}
                    className={`px-3 py-1 rounded text-base font-medium transition-colors duration-200 ${previewMode === 'dark' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                  >
                    <Moon className="w-5 h-5" />
                  </button>
                </div>
              )}
            </div>
            <div className={`flex items-center justify-center min-h-48 w-full rounded-xl transition-colors duration-200 ${previewMode === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}> 
              <button
                style={getButtonStyle(false, previewMode === 'dark')}
                onMouseEnter={e => Object.assign(e.currentTarget.style, getButtonStyle(true, previewMode === 'dark'))}
                onMouseLeave={e => Object.assign(e.currentTarget.style, getButtonStyle(false, previewMode === 'dark'))}
                className="transition-all duration-200"
              >
                {config.text}
              </button>
            </div>
          </section>

          {/* Export Panel */}
          <section className="bg-white dark:bg-neutral-900 rounded-2xl shadow-xl border border-gray-200 dark:border-neutral-800 p-8 sticky top-8 animate-fade-in">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-blue-700 dark:text-blue-300">Export</h2>
              <div className="flex space-x-2 bg-gray-100 dark:bg-neutral-800 rounded-lg p-1">
                <button
                  onClick={() => setExportFormat('css')}
                  className={`px-4 py-1 rounded text-base font-medium transition-colors duration-200 ${exportFormat === 'css' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                >CSS</button>
                <button
                  onClick={() => setExportFormat('tailwind')}
                  className={`px-4 py-1 rounded text-base font-medium transition-colors duration-200 ${exportFormat === 'tailwind' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                >Tailwind</button>
              </div>
            </div>
            {exportFormat === 'tailwind' && (
              <div className="mb-4 p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg flex gap-6">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="tailwindFormat"
                    value="className"
                    checked={tailwindFormat === 'className'}
                    onChange={e => setTailwindFormat(e.target.value as 'className' | 'class')}
                    className="mr-2 accent-blue-500"
                  />
                  <span className="text-sm">className</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="tailwindFormat"
                    value="class"
                    checked={tailwindFormat === 'class'}
                    onChange={e => setTailwindFormat(e.target.value as 'className' | 'class')}
                    className="mr-2 accent-blue-500"
                  />
                  <span className="text-sm">class</span>
                </label>
              </div>
            )}
            <div className="relative">
              <pre className="bg-gray-900 text-gray-100 p-6 rounded-xl overflow-x-auto text-base max-h-72 transition-all duration-200">
                <code>{getExportContent()}</code>
              </pre>
              <button
                onClick={() => copyToClipboard(getExportContent())}
                className={`absolute top-4 right-4 px-3 py-2 rounded-lg text-base font-medium flex items-center gap-2 transition-colors duration-200 ${copied ? 'bg-green-600 text-white' : 'bg-blue-700 hover:bg-blue-800 text-white'}`}
                aria-label="Copy code"
              >
                <Copy className="w-5 h-5" /> {copied ? 'Copied!' : 'Copy'}
              </button>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
};

// Helper: ColorInput
const ColorInput: React.FC<{ label: string; value: string; onChange: (v: string) => void }> = ({ label, value, onChange }) => (
  <div className="flex items-center gap-3">
    <span className="w-24 text-xs text-gray-600 dark:text-gray-400 font-semibold">{label}</span>
    <input type="color" value={value} onChange={e => onChange(e.target.value)} className="w-8 h-8 rounded-full border border-gray-300 dark:border-neutral-700" />
    <input type="text" value={value} onChange={e => onChange(e.target.value)} className="w-28 px-2 py-1 text-xs border border-gray-300 dark:border-neutral-700 rounded font-mono bg-gray-50 dark:bg-neutral-800" />
  </div>
);

export default ButtonGenerator;